#!/usr/bin/env python3
"""
Mobile App Automation Tool - Unique ID Migration Script

This script implements a comprehensive unique ID management system for test cases,
test suites, and execution tracking to improve status consistency and retry handling.

Features:
- Generates unique IDs for all test cases and test suites
- Updates database schemas with new ID fields
- Migrates existing execution tracking data
- Ensures backward compatibility
- Provides rollback capability

Usage:
    python unique_id_migration.py [--platform ios|android|both] [--dry-run] [--rollback]
"""

import os
import sys
import json
import sqlite3
import uuid
import argparse
import logging
import shutil
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unique_id_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UniqueIDMigration:
    """Handles the migration to unique ID system"""
    
    def __init__(self, platform='both', dry_run=False):
        self.platform = platform
        self.dry_run = dry_run
        self.backup_dir = f"migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.platforms = self._get_platforms()
        
        # ID mappings for tracking
        self.test_case_id_map = {}  # filename -> test_case_id
        self.test_suite_id_map = {}  # suite_name -> test_suite_id
        self.execution_id_map = {}  # old_execution -> new_execution_id
        
    def _get_platforms(self):
        """Get list of platforms to process"""
        if self.platform == 'both':
            return ['ios', 'android']
        return [self.platform]
    
    def _get_platform_paths(self, platform):
        """Get platform-specific paths"""
        if platform == 'ios':
            return {
                'app_dir': 'app',
                'db_path': 'data/test_execution.db',
                'test_cases_dir': None,  # Will be loaded from settings
                'reports_dir': 'reports'
            }
        else:  # android
            return {
                'app_dir': 'app_android',
                'db_path': 'data/test_execution_port_8081.db',
                'test_cases_dir': None,  # Will be loaded from settings
                'reports_dir': 'reports_android'
            }
    
    def _load_settings(self, platform):
        """Load settings to get configured directories"""
        paths = self._get_platform_paths(platform)
        try:
            # Import platform-specific database module
            sys.path.insert(0, paths['app_dir'])
            if platform == 'ios':
                from utils.database import get_db_path
                import config
            else:
                from utils.database import get_db_path
                import config

            # Get test cases directory from settings
            db_path = get_db_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Check if directory_paths table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='directory_paths'")
            if cursor.fetchone():
                # Get test cases directory from directory_paths table
                cursor.execute("SELECT path FROM directory_paths WHERE name = 'TEST_CASES'")
                result = cursor.fetchone()
                if result:
                    paths['test_cases_dir'] = result[0]
                else:
                    # Fallback to actual test cases directory
                    paths['test_cases_dir'] = '/Users/<USER>/Documents/automation-tool/test_cases'
            else:
                # Table doesn't exist, use actual test cases directory
                paths['test_cases_dir'] = '/Users/<USER>/Documents/automation-tool/test_cases'

            conn.close()
            return paths

        except Exception as e:
            logger.warning(f"Could not load settings for {platform}: {e}")
            # Use defaults - use the actual test cases directory
            paths['test_cases_dir'] = '/Users/<USER>/Documents/automation-tool/test_cases'
            return paths
    
    def generate_unique_id(self, prefix=''):
        """Generate a unique ID"""
        return f"{prefix}{uuid.uuid4().hex[:12]}"
    
    def create_backup(self):
        """Create backup of databases and critical files"""
        if self.dry_run:
            logger.info("DRY RUN: Would create backup directory")
            return
        
        logger.info(f"Creating backup in {self.backup_dir}")
        os.makedirs(self.backup_dir, exist_ok=True)
        
        for platform in self.platforms:
            paths = self._get_platform_paths(platform)
            platform_backup = os.path.join(self.backup_dir, platform)
            os.makedirs(platform_backup, exist_ok=True)
            
            # Backup database
            if os.path.exists(paths['db_path']):
                shutil.copy2(paths['db_path'], 
                           os.path.join(platform_backup, 'test_automation.db'))
                logger.info(f"Backed up {platform} database")
    
    def update_database_schema(self, platform):
        """Update database schema with new ID fields"""
        paths = self._get_platform_paths(platform)
        db_path = paths['db_path']

        if not os.path.exists(db_path):
            logger.warning(f"Database not found for {platform}: {db_path}")
            return False

        if self.dry_run:
            logger.info(f"DRY RUN: Would update {platform} database schema")
            # In dry run mode, we still need to check if the schema updates would work
            # but we won't actually modify the database
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # Check if columns already exist
                cursor.execute("PRAGMA table_info(test_cases)")
                test_cases_columns = [col[1] for col in cursor.fetchall()]

                cursor.execute("PRAGMA table_info(test_suites)")
                test_suites_columns = [col[1] for col in cursor.fetchall()]

                cursor.execute("PRAGMA table_info(execution_tracking)")
                execution_tracking_columns = [col[1] for col in cursor.fetchall()]

                conn.close()

                # Check what would be added
                if 'test_case_id' not in test_cases_columns:
                    logger.info(f"DRY RUN: Would add test_case_id column to test_cases table")
                else:
                    logger.info(f"DRY RUN: test_case_id column already exists in test_cases table")

                if 'test_suite_id' not in test_suites_columns:
                    logger.info(f"DRY RUN: Would add test_suite_id column to test_suites table")
                else:
                    logger.info(f"DRY RUN: test_suite_id column already exists in test_suites table")

                if 'test_execution_id' not in execution_tracking_columns:
                    logger.info(f"DRY RUN: Would add test_execution_id column to execution_tracking table")
                else:
                    logger.info(f"DRY RUN: test_execution_id column already exists in execution_tracking table")

                if 'test_case_id' not in execution_tracking_columns:
                    logger.info(f"DRY RUN: Would add test_case_id column to execution_tracking table")
                else:
                    logger.info(f"DRY RUN: test_case_id column already exists in execution_tracking table")

                return True

            except Exception as e:
                logger.error(f"DRY RUN: Error checking {platform} database schema: {e}")
                return False
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Add test_case_id to test_cases table (without UNIQUE constraint initially)
            try:
                cursor.execute("ALTER TABLE test_cases ADD COLUMN test_case_id TEXT")
                logger.info(f"Added test_case_id column to {platform} test_cases table")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"test_case_id column already exists in {platform} test_cases table")
                else:
                    raise

            # Add test_suite_id to test_suites table (without UNIQUE constraint initially)
            try:
                cursor.execute("ALTER TABLE test_suites ADD COLUMN test_suite_id TEXT")
                logger.info(f"Added test_suite_id column to {platform} test_suites table")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"test_suite_id column already exists in {platform} test_suites table")
                else:
                    raise
            
            # Add test_execution_id to execution_tracking table
            try:
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN test_execution_id TEXT")
                logger.info(f"Added test_execution_id column to {platform} execution_tracking table")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"test_execution_id column already exists in {platform} execution_tracking table")
                else:
                    raise
            
            # Add test_case_id to execution_tracking table
            try:
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN test_case_id TEXT")
                logger.info(f"Added test_case_id column to {platform} execution_tracking table")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"test_case_id column already exists in {platform} execution_tracking table")
                else:
                    raise
            
            # Create indexes for better performance (unique constraints will be added later after data population)
            try:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_id ON test_cases(test_case_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_suite_id ON test_suites(test_suite_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_execution_id ON execution_tracking(test_execution_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_execution_test_case_id ON execution_tracking(test_case_id)")
                logger.info(f"Created indexes for {platform} database")
            except sqlite3.OperationalError as e:
                logger.warning(f"Could not create some indexes for {platform}: {e}")
                pass  # Indexes might already exist or have conflicts
            
            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error updating {platform} database schema: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False

    def add_unique_constraints(self, platform):
        """Add unique constraints after data is populated"""
        paths = self._get_platform_paths(platform)
        db_path = paths['db_path']

        if not os.path.exists(db_path):
            logger.warning(f"Database not found for {platform}: {db_path}")
            return False

        if self.dry_run:
            logger.info(f"DRY RUN: Would add unique constraints for {platform}")
            return True

        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Create unique indexes (which enforce uniqueness)
            try:
                cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS unique_test_case_id ON test_cases(test_case_id) WHERE test_case_id IS NOT NULL")
                logger.info(f"Added unique constraint for test_case_id in {platform}")
            except sqlite3.OperationalError as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"Could not create unique constraint for test_case_id in {platform}: {e}")

            try:
                cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS unique_test_suite_id ON test_suites(test_suite_id) WHERE test_suite_id IS NOT NULL")
                logger.info(f"Added unique constraint for test_suite_id in {platform}")
            except sqlite3.OperationalError as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"Could not create unique constraint for test_suite_id in {platform}: {e}")

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error adding unique constraints for {platform}: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False
            
        except Exception as e:
            logger.error(f"Error updating {platform} database schema: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False
    
    def assign_test_case_ids(self, platform):
        """Assign unique IDs to all test cases"""
        paths = self._load_settings(platform)
        test_cases_dir = paths['test_cases_dir']
        
        if not os.path.exists(test_cases_dir):
            logger.warning(f"Test cases directory not found for {platform}: {test_cases_dir}")
            return False
        
        logger.info(f"Processing test cases in {test_cases_dir}")
        
        # Get all JSON test case files
        test_case_files = []
        for root, dirs, files in os.walk(test_cases_dir):
            for file in files:
                if file.endswith('.json'):
                    test_case_files.append(os.path.join(root, file))
        
        logger.info(f"Found {len(test_case_files)} test case files for {platform}")
        
        # Process each test case file
        for file_path in test_case_files:
            try:
                # Generate relative path for consistent identification
                rel_path = os.path.relpath(file_path, test_cases_dir)
                
                # Generate unique ID
                test_case_id = self.generate_unique_id('tc_')
                self.test_case_id_map[rel_path] = test_case_id
                
                if not self.dry_run:
                    # Update the JSON file with the test_case_id
                    with open(file_path, 'r') as f:
                        test_case_data = json.load(f)
                    
                    test_case_data['test_case_id'] = test_case_id
                    
                    with open(file_path, 'w') as f:
                        json.dump(test_case_data, f, indent=2)
                
                logger.info(f"Assigned ID {test_case_id} to {rel_path}")
                
            except Exception as e:
                logger.error(f"Error processing test case {file_path}: {e}")
        
        return True
    
    def assign_test_suite_ids(self, platform):
        """Assign unique IDs to all test suites"""
        paths = self._get_platform_paths(platform)
        db_path = paths['db_path']

        if not os.path.exists(db_path):
            logger.warning(f"Database not found for {platform}: {db_path}")
            return False

        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Check if test_suite_id column exists
            cursor.execute("PRAGMA table_info(test_suites)")
            columns = [col[1] for col in cursor.fetchall()]

            if 'test_suite_id' not in columns:
                if self.dry_run:
                    logger.info(f"DRY RUN: test_suite_id column doesn't exist yet, would be created in schema update")
                    # Get all test suites without the WHERE clause
                    cursor.execute("SELECT id, name FROM test_suites")
                    test_suites = cursor.fetchall()
                else:
                    logger.error(f"test_suite_id column doesn't exist in test_suites table for {platform}")
                    conn.close()
                    return False
            else:
                # Get all test suites that don't have IDs
                cursor.execute("SELECT id, name FROM test_suites WHERE test_suite_id IS NULL OR test_suite_id = ''")
                test_suites = cursor.fetchall()

            logger.info(f"Found {len(test_suites)} test suites without IDs for {platform}")

            for suite_id, suite_name in test_suites:
                test_suite_id = self.generate_unique_id('ts_')
                self.test_suite_id_map[suite_name] = test_suite_id

                if not self.dry_run and 'test_suite_id' in columns:
                    cursor.execute(
                        "UPDATE test_suites SET test_suite_id = ? WHERE id = ?",
                        (test_suite_id, suite_id)
                    )

                logger.info(f"{'DRY RUN: Would assign' if self.dry_run else 'Assigned'} ID {test_suite_id} to test suite '{suite_name}'")

            if not self.dry_run:
                conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error assigning test suite IDs for {platform}: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False

    def update_execution_tracking(self, platform):
        """Update execution tracking with new ID system"""
        paths = self._get_platform_paths(platform)
        db_path = paths['db_path']

        if not os.path.exists(db_path):
            logger.warning(f"Database not found for {platform}: {db_path}")
            return False

        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Check if new columns exist
            cursor.execute("PRAGMA table_info(execution_tracking)")
            columns = [col[1] for col in cursor.fetchall()]

            if 'test_case_id' not in columns or 'test_execution_id' not in columns:
                if self.dry_run:
                    logger.info(f"DRY RUN: New execution tracking columns don't exist yet, would be created in schema update")
                    # Get all execution tracking entries for dry run analysis
                    cursor.execute("SELECT id, filename, suite_id FROM execution_tracking")
                    tracking_entries = cursor.fetchall()
                else:
                    logger.error(f"Required columns don't exist in execution_tracking table for {platform}")
                    conn.close()
                    return False
            else:
                # Get all execution tracking entries without test_case_id
                cursor.execute("""
                    SELECT id, filename, suite_id
                    FROM execution_tracking
                    WHERE test_case_id IS NULL OR test_case_id = ''
                """)
                tracking_entries = cursor.fetchall()

            logger.info(f"Found {len(tracking_entries)} execution tracking entries to update for {platform}")

            updated_count = 0
            for entry_id, filename, suite_id in tracking_entries:
                # For existing execution tracking entries, we'll generate unique execution IDs
                # but leave test_case_id as NULL since we can't reliably map "unknown" filenames

                if not self.dry_run and 'test_execution_id' in columns:
                    # Check if this entry already has a test_execution_id
                    cursor.execute("SELECT test_execution_id FROM execution_tracking WHERE id = ?", (entry_id,))
                    result = cursor.fetchone()
                    current_exec_id = result[0] if result else None

                    if not current_exec_id:
                        test_execution_id = self.generate_unique_id('ex_')

                        cursor.execute("""
                            UPDATE execution_tracking
                            SET test_execution_id = ?
                            WHERE id = ?
                        """, (test_execution_id, entry_id))

                        updated_count += 1
                        logger.debug(f"Added execution ID {test_execution_id} to entry {entry_id}")
                elif self.dry_run:
                    test_execution_id = self.generate_unique_id('ex_')
                    updated_count += 1
                    logger.debug(f"DRY RUN: Would add execution ID {test_execution_id} to entry {entry_id}")

                # Note: test_case_id will remain NULL for existing entries with "unknown" filenames
                # Future executions will properly populate test_case_id when the tracking system is updated

            if not self.dry_run:
                conn.commit()
                logger.info(f"Updated {updated_count} execution tracking entries for {platform}")
            else:
                logger.info(f"DRY RUN: Would update {len(tracking_entries)} execution tracking entries for {platform}")

            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error updating execution tracking for {platform}: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False

    def update_data_json_files(self, platform):
        """Update existing data.json files with new ID system"""
        paths = self._load_settings(platform)
        reports_dir = paths['reports_dir']

        if not os.path.exists(reports_dir):
            logger.warning(f"Reports directory not found for {platform}: {reports_dir}")
            return False

        logger.info(f"Updating data.json files in {reports_dir}")

        updated_count = 0
        for root, dirs, files in os.walk(reports_dir):
            for file in files:
                if file == 'data.json':
                    file_path = os.path.join(root, file)
                    try:
                        if not self.dry_run:
                            with open(file_path, 'r') as f:
                                data = json.load(f)

                            # Add test_execution_id if not present
                            if 'test_execution_id' not in data:
                                data['test_execution_id'] = self.generate_unique_id('ex_')

                            # Update test case IDs in testCases
                            if 'testCases' in data:
                                for test_case in data['testCases']:
                                    if 'test_case_id' not in test_case:
                                        # Try to find matching test_case_id
                                        tc_name = test_case.get('name', '')
                                        tc_id = test_case.get('id', '')

                                        # Look for matching ID in our mapping
                                        test_case_id = None
                                        for file_path_key, mapped_id in self.test_case_id_map.items():
                                            if tc_name in file_path_key or tc_id in file_path_key:
                                                test_case_id = mapped_id
                                                break

                                        if test_case_id:
                                            test_case['test_case_id'] = test_case_id
                                        else:
                                            # Generate new ID if no match found
                                            test_case['test_case_id'] = self.generate_unique_id('tc_')

                            # Add test_suite_id if not present
                            if 'test_suite_id' not in data:
                                suite_name = data.get('name', '')
                                if suite_name in self.test_suite_id_map:
                                    data['test_suite_id'] = self.test_suite_id_map[suite_name]
                                else:
                                    data['test_suite_id'] = self.generate_unique_id('ts_')

                            with open(file_path, 'w') as f:
                                json.dump(data, f, indent=2)

                        updated_count += 1
                        logger.debug(f"Updated data.json: {file_path}")

                    except Exception as e:
                        logger.error(f"Error updating data.json {file_path}: {e}")

        if self.dry_run:
            logger.info(f"DRY RUN: Would update {updated_count} data.json files for {platform}")
        else:
            logger.info(f"Updated {updated_count} data.json files for {platform}")

        return True

    def validate_migration(self, platform):
        """Validate the migration results"""
        paths = self._get_platform_paths(platform)
        db_path = paths['db_path']

        if not os.path.exists(db_path):
            return False

        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Check what columns exist
            cursor.execute("PRAGMA table_info(test_cases)")
            test_cases_columns = [col[1] for col in cursor.fetchall()]

            cursor.execute("PRAGMA table_info(test_suites)")
            test_suites_columns = [col[1] for col in cursor.fetchall()]

            cursor.execute("PRAGMA table_info(execution_tracking)")
            execution_tracking_columns = [col[1] for col in cursor.fetchall()]

            # Initialize counters
            test_cases_with_ids = 0
            total_test_cases = 0
            test_suites_with_ids = 0
            total_test_suites = 0
            duplicate_test_case_ids = []
            duplicate_test_suite_ids = []

            # Check test_cases table only if column exists
            if 'test_case_id' in test_cases_columns:
                cursor.execute("SELECT COUNT(*) FROM test_cases WHERE test_case_id IS NOT NULL AND test_case_id != ''")
                test_cases_with_ids = cursor.fetchone()[0]

                # Check for duplicate IDs
                cursor.execute("SELECT test_case_id, COUNT(*) FROM test_cases WHERE test_case_id IS NOT NULL GROUP BY test_case_id HAVING COUNT(*) > 1")
                duplicate_test_case_ids = cursor.fetchall()
            else:
                logger.info(f"test_case_id column doesn't exist yet in test_cases table for {platform}")

            cursor.execute("SELECT COUNT(*) FROM test_cases")
            total_test_cases = cursor.fetchone()[0]

            # Check test_suites table only if column exists
            if 'test_suite_id' in test_suites_columns:
                cursor.execute("SELECT COUNT(*) FROM test_suites WHERE test_suite_id IS NOT NULL AND test_suite_id != ''")
                test_suites_with_ids = cursor.fetchone()[0]

                # Check for duplicate IDs
                cursor.execute("SELECT test_suite_id, COUNT(*) FROM test_suites WHERE test_suite_id IS NOT NULL GROUP BY test_suite_id HAVING COUNT(*) > 1")
                duplicate_test_suite_ids = cursor.fetchall()
            else:
                logger.info(f"test_suite_id column doesn't exist yet in test_suites table for {platform}")

            cursor.execute("SELECT COUNT(*) FROM test_suites")
            total_test_suites = cursor.fetchone()[0]

            conn.close()

            # Report validation results
            logger.info(f"Validation results for {platform}:")
            logger.info(f"  Test cases with IDs: {test_cases_with_ids}/{total_test_cases}")
            logger.info(f"  Test suites with IDs: {test_suites_with_ids}/{total_test_suites}")

            if duplicate_test_case_ids:
                logger.error(f"  Duplicate test case IDs found: {duplicate_test_case_ids}")
                return False

            if duplicate_test_suite_ids:
                logger.error(f"  Duplicate test suite IDs found: {duplicate_test_suite_ids}")
                return False

            logger.info(f"  No duplicate IDs found for {platform}")
            return True

        except Exception as e:
            logger.error(f"Error validating migration for {platform}: {e}")
            return False

    def rollback_migration(self):
        """Rollback the migration using backup files"""
        if not os.path.exists(self.backup_dir):
            logger.error(f"Backup directory not found: {self.backup_dir}")
            return False

        logger.info(f"Rolling back migration from backup: {self.backup_dir}")

        success = True
        for platform in self.platforms:
            try:
                paths = self._get_platform_paths(platform)
                platform_backup = os.path.join(self.backup_dir, platform)
                backup_db = os.path.join(platform_backup, 'test_automation.db')

                if os.path.exists(backup_db):
                    shutil.copy2(backup_db, paths['db_path'])
                    logger.info(f"Restored {platform} database from backup")
                else:
                    logger.warning(f"No backup database found for {platform}")
                    success = False

            except Exception as e:
                logger.error(f"Error rolling back {platform}: {e}")
                success = False

        return success

    def run_migration(self):
        """Run the complete migration process"""
        logger.info("Starting unique ID migration process")
        logger.info(f"Platform(s): {', '.join(self.platforms)}")
        logger.info(f"Dry run: {self.dry_run}")

        # Create backup
        self.create_backup()

        success = True
        for platform in self.platforms:
            logger.info(f"\n=== Processing {platform.upper()} platform ===")

            # Update database schema
            if not self.update_database_schema(platform):
                logger.error(f"Failed to update database schema for {platform}")
                success = False
                continue

            # Assign test case IDs
            if not self.assign_test_case_ids(platform):
                logger.error(f"Failed to assign test case IDs for {platform}")
                success = False
                continue

            # Assign test suite IDs
            if not self.assign_test_suite_ids(platform):
                logger.error(f"Failed to assign test suite IDs for {platform}")
                success = False
                continue

            # Update execution tracking
            if not self.update_execution_tracking(platform):
                logger.error(f"Failed to update execution tracking for {platform}")
                success = False
                continue

            # Update data.json files
            if not self.update_data_json_files(platform):
                logger.error(f"Failed to update data.json files for {platform}")
                success = False
                continue

            # Add unique constraints after data is populated
            if not self.add_unique_constraints(platform):
                logger.error(f"Failed to add unique constraints for {platform}")
                success = False
                continue

            # Validate migration
            if not self.validate_migration(platform):
                logger.error(f"Migration validation failed for {platform}")
                success = False
                continue

            logger.info(f"Successfully completed migration for {platform}")

        if success:
            logger.info("\n=== MIGRATION COMPLETED SUCCESSFULLY ===")
            logger.info("All platforms have been migrated to the unique ID system")
            logger.info(f"Backup created at: {self.backup_dir}")
        else:
            logger.error("\n=== MIGRATION FAILED ===")
            logger.error("Some platforms failed to migrate. Check logs for details.")
            logger.info(f"Use --rollback to restore from backup: {self.backup_dir}")

        return success


def main():
    """Main entry point for the migration script"""
    parser = argparse.ArgumentParser(
        description='Mobile App Automation Tool - Unique ID Migration Script',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python unique_id_migration.py --platform ios --dry-run
  python unique_id_migration.py --platform both
  python unique_id_migration.py --rollback
        """
    )

    parser.add_argument(
        '--platform',
        choices=['ios', 'android', 'both'],
        default='both',
        help='Platform to migrate (default: both)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without making changes'
    )

    parser.add_argument(
        '--rollback',
        action='store_true',
        help='Rollback the migration using the most recent backup'
    )

    parser.add_argument(
        '--backup-dir',
        help='Specific backup directory to use for rollback'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Handle rollback
    if args.rollback:
        if args.backup_dir:
            migration = UniqueIDMigration()
            migration.backup_dir = args.backup_dir
        else:
            # Find the most recent backup
            backup_dirs = [d for d in os.listdir('.') if d.startswith('migration_backup_')]
            if not backup_dirs:
                logger.error("No backup directories found")
                return 1

            backup_dirs.sort(reverse=True)
            migration = UniqueIDMigration()
            migration.backup_dir = backup_dirs[0]
            logger.info(f"Using most recent backup: {migration.backup_dir}")

        if migration.rollback_migration():
            logger.info("Rollback completed successfully")
            return 0
        else:
            logger.error("Rollback failed")
            return 1

    # Run migration
    migration = UniqueIDMigration(platform=args.platform, dry_run=args.dry_run)

    if migration.run_migration():
        logger.info("Migration completed successfully")
        return 0
    else:
        logger.error("Migration failed")
        return 1


if __name__ == '__main__':
    sys.exit(main())
