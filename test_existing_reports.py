#!/usr/bin/env python3
"""
Test script to validate that the execution tracking fixes work with existing test reports.

This script tests:
1. Import existing data.json files into the database
2. Verify retry logic works correctly with imported data
3. Test status determination logic for complex retry scenarios
4. Validate report generation from imported data

Usage: python test_existing_reports.py
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.database import track_test_execution, get_db_path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_import_existing_report():
    """Test importing an existing report and verify retry logic"""
    logger.info("=" * 60)
    logger.info("Testing Import of Existing Report")
    logger.info("=" * 60)
    
    # Path to existing report
    report_path = "/Users/<USER>/Documents/automation-tool/reports/test_retry_scenario/data.json"
    
    if not os.path.exists(report_path):
        logger.error(f"Report file not found: {report_path}")
        return False
    
    # Load the existing report
    with open(report_path, 'r') as f:
        report_data = json.load(f)
    
    test_execution_id = report_data.get('test_execution_id')
    test_suite_id = report_data.get('test_suite_id')
    
    logger.info(f"Importing report: {report_data.get('name')}")
    logger.info(f"Execution ID: {test_execution_id}")
    logger.info(f"Suite ID: {test_suite_id}")
    
    # Clear any existing data for this execution
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()
    cursor.execute("DELETE FROM execution_tracking WHERE test_execution_id = ?", (test_execution_id,))
    conn.commit()
    conn.close()
    
    # Import each test case and its steps
    imported_count = 0
    for test_case_idx, test_case in enumerate(report_data.get('testCases', [])):
        test_case_id = test_case.get('test_case_id')
        test_case_name = test_case.get('name', f'Test Case {test_case_idx}')
        test_case_filename = test_case.get('filename', f'{test_case_name}.json')
        
        logger.info(f"Importing test case: {test_case_name} (ID: {test_case_id})")
        
        # Import each step/action
        for step_idx, step in enumerate(test_case.get('steps', [])):
            action_id = step.get('action_id')
            action_type = step.get('action_type', 'unknown')
            step_status = step.get('status', 'unknown')
            retry_count = step.get('retry_count', 0)
            
            # Import this step into the database
            result = track_test_execution(
                suite_id=test_suite_id,
                test_idx=test_case_idx,
                filename=test_case_filename,
                status=step_status,
                retry_count=retry_count,
                max_retries=0,
                error=step.get('error'),
                in_progress=False,
                step_idx=step_idx,
                action_type=action_type,
                action_params={'description': step.get('description', '')},
                action_id=action_id,
                test_case_id=test_case_id,
                test_execution_id=test_execution_id
            )
            
            if result:
                imported_count += 1
            else:
                logger.error(f"Failed to import step {step_idx} for action {action_id}")
    
    logger.info(f"Successfully imported {imported_count} steps")
    
    # Verify the import results
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()
    
    # Check unique actions and their final statuses
    cursor.execute("""
        SELECT action_id, COUNT(*) as step_count, MAX(retry_count) as max_retry, 
               status as final_status
        FROM execution_tracking 
        WHERE test_execution_id = ?
        GROUP BY action_id
        ORDER BY action_id
    """, (test_execution_id,))
    
    action_results = cursor.fetchall()
    
    logger.info("Action Results:")
    for action_id, step_count, max_retry, final_status in action_results:
        logger.info(f"  {action_id}: {step_count} steps, max retry: {max_retry}, final status: {final_status}")
    
    # Verify specific retry scenarios
    # retry_action_001 should have max retry of 2 and final status of 'passed'
    retry_action_001 = next((r for r in action_results if r[0] == 'retry_action_001'), None)
    
    success = (
        imported_count > 0 and
        retry_action_001 is not None and
        retry_action_001[2] == 2 and  # max_retry should be 2
        retry_action_001[3] == 'passed'  # final_status should be 'passed'
    )
    
    conn.close()
    
    logger.info(f"Import test result: {'PASS' if success else 'FAIL'}")
    return success

def test_status_determination():
    """Test the status determination logic for complex retry scenarios"""
    logger.info("=" * 60)
    logger.info("Testing Status Determination Logic")
    logger.info("=" * 60)
    
    # Test data with complex retry scenarios
    test_execution_id = "status_test_001"
    test_suite_id = "suite_status_001"
    test_case_id = "case_status_001"
    
    # Clear any existing data
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()
    cursor.execute("DELETE FROM execution_tracking WHERE test_execution_id = ?", (test_execution_id,))
    conn.commit()
    conn.close()
    
    # Scenario 1: Action that fails then passes
    scenarios = [
        # Action 1: Failed -> Failed -> Passed (should be PASSED)
        {"action_id": "status_action_001", "status": "failed", "retry_count": 0},
        {"action_id": "status_action_001", "status": "failed", "retry_count": 1},
        {"action_id": "status_action_001", "status": "passed", "retry_count": 2},
        
        # Action 2: Failed -> Passed (should be PASSED)
        {"action_id": "status_action_002", "status": "failed", "retry_count": 0},
        {"action_id": "status_action_002", "status": "passed", "retry_count": 1},
        
        # Action 3: Failed -> Failed -> Failed (should be FAILED)
        {"action_id": "status_action_003", "status": "failed", "retry_count": 0},
        {"action_id": "status_action_003", "status": "failed", "retry_count": 1},
        {"action_id": "status_action_003", "status": "failed", "retry_count": 2},
    ]
    
    # Import all scenarios
    for step_idx, scenario in enumerate(scenarios):
        track_test_execution(
            suite_id=test_suite_id,
            test_idx=0,
            filename="status_test.json",
            status=scenario["status"],
            retry_count=scenario["retry_count"],
            max_retries=3,
            error=None,
            in_progress=False,
            step_idx=step_idx,
            action_type="tap",
            action_params={'description': f'Status test step {step_idx}'},
            action_id=scenario["action_id"],
            test_case_id=test_case_id,
            test_execution_id=test_execution_id
        )
    
    # Verify the final statuses
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT action_id, status, retry_count
        FROM execution_tracking 
        WHERE test_execution_id = ?
        ORDER BY action_id, retry_count DESC
    """, (test_execution_id,))
    
    all_results = cursor.fetchall()
    
    # Group by action_id and get the latest (highest retry_count) status
    final_statuses = {}
    for action_id, status, retry_count in all_results:
        if action_id not in final_statuses or retry_count > final_statuses[action_id][1]:
            final_statuses[action_id] = (status, retry_count)
    
    conn.close()
    
    logger.info("Final Action Statuses:")
    for action_id, (status, retry_count) in final_statuses.items():
        logger.info(f"  {action_id}: {status} (retry {retry_count})")
    
    # Verify expected results
    expected_results = {
        "status_action_001": "passed",  # Should be passed after 2 retries
        "status_action_002": "passed",  # Should be passed after 1 retry
        "status_action_003": "failed",  # Should remain failed after 2 retries
    }
    
    success = True
    for action_id, expected_status in expected_results.items():
        actual_status = final_statuses.get(action_id, (None, None))[0]
        if actual_status != expected_status:
            logger.error(f"Status mismatch for {action_id}: expected {expected_status}, got {actual_status}")
            success = False
    
    logger.info(f"Status determination test result: {'PASS' if success else 'FAIL'}")
    return success

def main():
    """Run all tests"""
    logger.info("Starting validation of execution tracking fixes with existing reports...")
    
    results = []
    
    # Test 1: Import existing report
    results.append(test_import_existing_report())
    
    # Test 2: Status determination logic
    results.append(test_status_determination())
    
    # Generate final report
    total_tests = len(results)
    passed_tests = sum(results)
    failed_tests = total_tests - passed_tests
    
    logger.info("=" * 60)
    logger.info("FINAL VALIDATION REPORT")
    logger.info("=" * 60)
    logger.info(f"Total Tests: {total_tests}")
    logger.info(f"Passed: {passed_tests}")
    logger.info(f"Failed: {failed_tests}")
    logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests == 0:
        logger.info("🎉 ALL VALIDATION TESTS PASSED! The fixes work correctly with existing reports.")
        return True
    else:
        logger.warning(f"⚠️  {failed_tests} validation test(s) failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
