#!/usr/bin/env python3
"""
Comprehensive test script to validate the test execution tracking and reporting system fixes.

This script tests:
1. Database retry logic (UPDATE vs INSERT)
2. Import functionality from data.json files
3. Data synchronization between UI, database, and data.json
4. Comprehensive logging
5. Multi-step action UI enhancements

Usage: python test_execution_tracking_fixes.py
"""

import os
import sys
import json
import sqlite3
import logging
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.database import (
    track_test_execution, 
    sync_execution_data_to_json, 
    log_execution_tracking_change,
    get_db_path
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestExecutionTrackingFixes:
    def __init__(self):
        self.test_execution_id = "test_exec_001"
        self.test_suite_id = "test_suite_001"
        self.test_case_id = "test_case_001"
        self.action_id = "action_001"
        self.reports_dir = tempfile.mkdtemp(prefix="test_reports_")
        self.test_results = []
        
    def setUp(self):
        """Set up test environment"""
        logger.info("Setting up test environment...")
        
        # Create test reports directory structure
        execution_dir = os.path.join(self.reports_dir, f"testsuite_execution_{self.test_execution_id}")
        os.makedirs(execution_dir, exist_ok=True)
        
        # Create a sample data.json file
        sample_data = {
            "name": "Test Suite for Validation",
            "test_execution_id": self.test_execution_id,
            "test_suite_id": self.test_suite_id,
            "timestamp": datetime.now().isoformat(),
            "status": "failed",
            "testCases": [
                {
                    "name": "Sample Test Case",
                    "test_case_id": self.test_case_id,
                    "filename": "sample_test.json",
                    "status": "failed",
                    "steps": [
                        {
                            "action_id": self.action_id,
                            "action_type": "tap",
                            "status": "failed",
                            "retry_count": 0,
                            "description": "Initial execution - failed",
                            "timestamp": datetime.now().isoformat()
                        }
                    ]
                }
            ]
        }
        
        data_json_path = os.path.join(execution_dir, "data.json")
        with open(data_json_path, 'w') as f:
            json.dump(sample_data, f, indent=2)
            
        logger.info(f"Created test data.json at: {data_json_path}")
        
    def test_database_retry_logic(self):
        """Test 1: Database retry logic - UPDATE vs INSERT"""
        logger.info("=" * 60)
        logger.info("TEST 1: Database Retry Logic")
        logger.info("=" * 60)
        
        try:
            # First execution - should INSERT
            result1 = track_test_execution(
                suite_id=self.test_suite_id,
                test_idx=0,
                filename="test_case.json",
                status="failed",
                retry_count=0,
                max_retries=3,
                error="Initial failure",
                in_progress=False,
                step_idx=0,
                action_type="tap",
                action_params={"description": "Tap login button"},
                action_id=self.action_id,
                test_case_id=self.test_case_id,
                test_execution_id=self.test_execution_id
            )
            
            # Check database state after first execution
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM execution_tracking 
                WHERE test_execution_id = ? AND test_case_id = ? AND action_id = ?
            """, (self.test_execution_id, self.test_case_id, self.action_id))
            count_after_first = cursor.fetchone()[0]
            conn.close()
            
            logger.info(f"After first execution: {count_after_first} entries in database")
            
            # Second execution (retry) - should UPDATE, not INSERT
            result2 = track_test_execution(
                suite_id=self.test_suite_id,
                test_idx=0,
                filename="test_case.json",
                status="passed",
                retry_count=1,
                max_retries=3,
                error=None,
                in_progress=False,
                step_idx=0,
                action_type="tap",
                action_params={"description": "Tap login button"},
                action_id=self.action_id,
                test_case_id=self.test_case_id,
                test_execution_id=self.test_execution_id
            )
            
            # Check database state after retry
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*), MAX(retry_count), status FROM execution_tracking 
                WHERE test_execution_id = ? AND test_case_id = ? AND action_id = ?
            """, (self.test_execution_id, self.test_case_id, self.action_id))
            count_after_retry, max_retry, final_status = cursor.fetchone()
            conn.close()
            
            logger.info(f"After retry: {count_after_retry} entries, max retry: {max_retry}, status: {final_status}")
            
            # Validate results
            success = (
                result1 and result2 and
                count_after_first == 1 and
                count_after_retry == 1 and  # Should still be 1 (UPDATE, not INSERT)
                max_retry == 1 and
                final_status == "passed"
            )
            
            self.test_results.append({
                "test": "Database Retry Logic",
                "success": success,
                "details": f"Entries: {count_after_retry}, Retry: {max_retry}, Status: {final_status}"
            })
            
            logger.info(f"TEST 1 RESULT: {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            logger.error(f"TEST 1 FAILED with exception: {str(e)}")
            self.test_results.append({
                "test": "Database Retry Logic",
                "success": False,
                "details": f"Exception: {str(e)}"
            })
            
    def test_data_synchronization(self):
        """Test 2: Data synchronization between database and data.json"""
        logger.info("=" * 60)
        logger.info("TEST 2: Data Synchronization")
        logger.info("=" * 60)
        
        try:
            # Test synchronization function
            sync_result = sync_execution_data_to_json(self.test_execution_id, self.reports_dir)
            
            # Check if data.json was updated
            execution_dir = os.path.join(self.reports_dir, f"testsuite_execution_{self.test_execution_id}")
            data_json_path = os.path.join(execution_dir, "data.json")
            
            if os.path.exists(data_json_path):
                with open(data_json_path, 'r') as f:
                    updated_data = json.load(f)
                
                # Check if the data reflects the database state
                test_case = updated_data.get('testCases', [{}])[0]
                steps = test_case.get('steps', [])
                
                # Should have the updated status from the retry
                has_passed_step = any(step.get('status') == 'passed' for step in steps)
                
                logger.info(f"Sync result: {sync_result}")
                logger.info(f"Updated data.json has passed step: {has_passed_step}")
                
                success = sync_result and has_passed_step
            else:
                success = False
                logger.error("data.json file not found after sync")
            
            self.test_results.append({
                "test": "Data Synchronization",
                "success": success,
                "details": f"Sync result: {sync_result}, File exists: {os.path.exists(data_json_path)}"
            })
            
            logger.info(f"TEST 2 RESULT: {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            logger.error(f"TEST 2 FAILED with exception: {str(e)}")
            self.test_results.append({
                "test": "Data Synchronization",
                "success": False,
                "details": f"Exception: {str(e)}"
            })
            
    def test_comprehensive_logging(self):
        """Test 3: Comprehensive logging functionality"""
        logger.info("=" * 60)
        logger.info("TEST 3: Comprehensive Logging")
        logger.info("=" * 60)
        
        try:
            # Test the logging function directly
            log_execution_tracking_change(
                test_execution_id="test_log_001",
                test_suite_id="suite_log_001",
                test_case_id="case_log_001",
                action_id="action_log_001",
                old_status="failed",
                new_status="passed",
                retry_count=2,
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                operation_type="UPDATE"
            )
            
            # Check if log files were created
            log_files_exist = (
                os.path.exists("execution_tracking.log") or
                any("execution_tracking" in f for f in os.listdir(".") if f.endswith(".log"))
            )
            
            logger.info(f"Log files exist: {log_files_exist}")
            
            success = log_files_exist
            
            self.test_results.append({
                "test": "Comprehensive Logging",
                "success": success,
                "details": f"Log files created: {log_files_exist}"
            })
            
            logger.info(f"TEST 3 RESULT: {'PASS' if success else 'FAIL'}")
            
        except Exception as e:
            logger.error(f"TEST 3 FAILED with exception: {str(e)}")
            self.test_results.append({
                "test": "Comprehensive Logging",
                "success": False,
                "details": f"Exception: {str(e)}"
            })

    def test_import_functionality(self):
        """Test 4: Import functionality from data.json files"""
        logger.info("=" * 60)
        logger.info("TEST 4: Import Functionality")
        logger.info("=" * 60)

        try:
            # Clear the database first
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            cursor.execute("DELETE FROM execution_tracking WHERE test_execution_id = ?", (self.test_execution_id,))
            conn.commit()
            conn.close()

            # Create a more complex data.json with retry scenarios
            execution_dir = os.path.join(self.reports_dir, f"testsuite_execution_{self.test_execution_id}")
            complex_data = {
                "name": "Import Test Suite",
                "test_execution_id": self.test_execution_id,
                "test_suite_id": self.test_suite_id,
                "timestamp": datetime.now().isoformat(),
                "status": "passed",
                "testCases": [
                    {
                        "name": "Test Case with Retries",
                        "test_case_id": self.test_case_id,
                        "filename": "retry_test.json",
                        "status": "passed",
                        "steps": [
                            {
                                "action_id": "import_action_001",
                                "action_type": "tap",
                                "status": "failed",
                                "retry_count": 0,
                                "description": "First attempt - failed",
                                "timestamp": datetime.now().isoformat()
                            },
                            {
                                "action_id": "import_action_001",
                                "action_type": "tap",
                                "status": "failed",
                                "retry_count": 1,
                                "description": "Second attempt - failed",
                                "timestamp": datetime.now().isoformat()
                            },
                            {
                                "action_id": "import_action_001",
                                "action_type": "tap",
                                "status": "passed",
                                "retry_count": 2,
                                "description": "Third attempt - passed",
                                "timestamp": datetime.now().isoformat()
                            }
                        ]
                    }
                ]
            }

            data_json_path = os.path.join(execution_dir, "data.json")
            with open(data_json_path, 'w') as f:
                json.dump(complex_data, f, indent=2)

            # Import the data using the new import functionality
            # Simulate the import process
            imported_count = 0
            for test_case_idx, test_case in enumerate(complex_data.get('testCases', [])):
                test_case_id = test_case.get('test_case_id')
                test_case_filename = test_case.get('filename', f'{test_case.get("name", "")}.json')

                for step_idx, step in enumerate(test_case.get('steps', [])):
                    action_id = step.get('action_id')
                    action_type = step.get('action_type', 'unknown')
                    step_status = step.get('status', 'unknown')
                    retry_count = step.get('retry_count', 0)

                    track_test_execution(
                        suite_id=self.test_suite_id,
                        test_idx=test_case_idx,
                        filename=test_case_filename,
                        status=step_status,
                        retry_count=retry_count,
                        max_retries=0,
                        error=step.get('error'),
                        in_progress=False,
                        step_idx=step_idx,
                        action_type=action_type,
                        action_params={'description': step.get('description', '')},
                        action_id=action_id,
                        test_case_id=test_case_id,
                        test_execution_id=self.test_execution_id
                    )
                    imported_count += 1

            # Verify the import results
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(DISTINCT action_id), MAX(retry_count)
                FROM execution_tracking
                WHERE test_execution_id = ?
            """, (self.test_execution_id,))
            unique_actions, max_retry = cursor.fetchone()
            conn.close()

            logger.info(f"Imported {imported_count} steps")
            logger.info(f"Unique actions in DB: {unique_actions}, Max retry: {max_retry}")

            # Should have 1 unique action with max retry of 2
            success = unique_actions == 1 and max_retry == 2

            self.test_results.append({
                "test": "Import Functionality",
                "success": success,
                "details": f"Imported: {imported_count}, Unique actions: {unique_actions}, Max retry: {max_retry}"
            })

            logger.info(f"TEST 4 RESULT: {'PASS' if success else 'FAIL'}")

        except Exception as e:
            logger.error(f"TEST 4 FAILED with exception: {str(e)}")
            self.test_results.append({
                "test": "Import Functionality",
                "success": False,
                "details": f"Exception: {str(e)}"
            })

    def test_database_schema(self):
        """Test 5: Database schema validation"""
        logger.info("=" * 60)
        logger.info("TEST 5: Database Schema Validation")
        logger.info("=" * 60)

        try:
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()

            # Check if required columns exist
            cursor.execute("PRAGMA table_info(execution_tracking)")
            columns = [row[1] for row in cursor.fetchall()]

            required_columns = [
                'test_execution_id', 'test_case_id', 'action_id',
                'retry_count', 'status', 'execution_result'
            ]

            missing_columns = [col for col in required_columns if col not in columns]

            # Check if indexes exist
            cursor.execute("PRAGMA index_list(execution_tracking)")
            indexes = [row[1] for row in cursor.fetchall()]

            conn.close()

            logger.info(f"Available columns: {columns}")
            logger.info(f"Missing columns: {missing_columns}")
            logger.info(f"Available indexes: {indexes}")

            success = len(missing_columns) == 0

            self.test_results.append({
                "test": "Database Schema Validation",
                "success": success,
                "details": f"Missing columns: {missing_columns}, Indexes: {len(indexes)}"
            })

            logger.info(f"TEST 5 RESULT: {'PASS' if success else 'FAIL'}")

        except Exception as e:
            logger.error(f"TEST 5 FAILED with exception: {str(e)}")
            self.test_results.append({
                "test": "Database Schema Validation",
                "success": False,
                "details": f"Exception: {str(e)}"
            })

    def cleanup(self):
        """Clean up test environment"""
        logger.info("Cleaning up test environment...")

        # Remove test reports directory
        if os.path.exists(self.reports_dir):
            shutil.rmtree(self.reports_dir)

        # Clean up test data from database
        try:
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()
            cursor.execute("DELETE FROM execution_tracking WHERE test_execution_id = ?", (self.test_execution_id,))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.warning(f"Failed to clean up database: {str(e)}")

    def run_all_tests(self):
        """Run all tests and generate report"""
        logger.info("Starting comprehensive test execution tracking validation...")

        try:
            self.setUp()

            # Run all tests
            self.test_database_retry_logic()
            self.test_data_synchronization()
            self.test_comprehensive_logging()
            self.test_import_functionality()
            self.test_database_schema()

            # Generate final report
            self.generate_report()

        finally:
            self.cleanup()

    def generate_report(self):
        """Generate final test report"""
        logger.info("=" * 60)
        logger.info("FINAL TEST REPORT")
        logger.info("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info("")

        for result in self.test_results:
            status = "PASS" if result['success'] else "FAIL"
            logger.info(f"{status}: {result['test']} - {result['details']}")

        logger.info("=" * 60)

        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED! The execution tracking fixes are working correctly.")
        else:
            logger.warning(f"⚠️  {failed_tests} test(s) failed. Please review the implementation.")

        return failed_tests == 0

if __name__ == "__main__":
    tester = TestExecutionTrackingFixes()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
