{"name": "Import Retry Test Suite", "test_execution_id": "ex_import_test_001", "test_suite_id": "ts_import_test_001", "timestamp": "2025-06-27T21:24:13.801108", "status": "failed", "testCases": [{"name": "Test Case That Failed Then Passed on Retry", "test_case_id": "tc_import_test_001", "status": "failed", "steps": [{"action_id": "import_action_001", "action_type": "tap", "status": "failed", "description": "Original execution - failed", "timestamp": "2025-06-27T18:13:06"}, {"action_id": "import_action_001", "action_type": "tap", "status": "passed", "description": "Retry execution - passed", "timestamp": "2025-06-27T21:02:45"}, {"action_id": "import_action_002", "action_type": "input_text", "status": "passed", "description": "Other action - passed"}]}]}