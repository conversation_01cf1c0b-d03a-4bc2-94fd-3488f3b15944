{"name": "Retry Scenario Test Suite", "test_execution_id": "ex_test_retry_001", "test_suite_id": "ts_test_retry_001", "timestamp": "2025-06-27T21:14:58.842304", "status": "unknown", "testCases": [{"name": "Test Case with Failed → Passed <PERSON>try", "test_case_id": "tc_retry_test_001", "status": "failed", "steps": [{"action_id": "retry_action_001", "action_type": "tap", "status": "failed", "description": "First attempt - failed"}, {"action_id": "retry_action_001", "action_type": "tap", "status": "failed", "description": "Second attempt - failed"}, {"action_id": "retry_action_001", "action_type": "tap", "status": "passed", "description": "Third attempt - passed"}, {"action_id": "other_action_001", "action_type": "input_text", "status": "passed", "description": "Other action - passed"}]}, {"name": "Test Case with Multiple Retry Actions", "test_case_id": "tc_retry_test_002", "status": "failed", "steps": [{"action_id": "retry_action_002", "action_type": "tap", "status": "failed", "description": "Action 2 - first attempt failed"}, {"action_id": "retry_action_003", "action_type": "input_text", "status": "failed", "description": "Action 3 - first attempt failed"}, {"action_id": "retry_action_002", "action_type": "tap", "status": "passed", "description": "Action 2 - second attempt passed"}, {"action_id": "retry_action_003", "action_type": "input_text", "status": "passed", "description": "Action 3 - second attempt passed"}]}, {"name": "Test Case with <PERSON><PERSON>e Failure", "test_case_id": "tc_retry_test_003", "status": "failed", "steps": [{"action_id": "failed_action_001", "action_type": "tap", "status": "passed", "description": "First action - passed"}, {"action_id": "failed_action_002", "action_type": "tap", "status": "failed", "description": "Second action - failed (no retry)"}]}]}