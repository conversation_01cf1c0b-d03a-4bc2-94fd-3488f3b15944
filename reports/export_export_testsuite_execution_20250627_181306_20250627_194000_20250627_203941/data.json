{"name": "UI Execution 27/06/2025, 19:20:44", "testCases": [{"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            53 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2708ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2248ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1712ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2186ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1415ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2471ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2911ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2850ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2019ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "2245ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show\")]", "status": "passed", "duration": "2226ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1556ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2513ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3068ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10019ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*", "status": "passed", "duration": "2035ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::* with fallback: Coordinates (98, 308)", "status": "passed", "duration": "34406ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2769ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "1895ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "3103ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Swipe up till element accessibilityid: \"Learn more about AfterPay\" is visible", "status": "passed", "duration": "5666ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "5463ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "unknown", "duration": "13773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2310ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "4669ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "1473ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2268ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "4659ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2413ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "2710ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1337ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2176ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3278ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2299ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2352ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2718ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2096ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2607ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2188ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"mat\"", "status": "passed", "duration": "2439ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2076ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2595ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2627ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5789ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2962ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5685ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1087ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23916ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            28 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2197ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4248ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1185ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2084ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3775ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "failed", "duration": "3710ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "unknown", "duration": "3513ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "unknown", "duration": "7785ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "unknown", "duration": "2490ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "3976ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "unknown", "duration": "2715ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "unknown", "duration": "2113ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "4366ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"Brunswick\"", "status": "unknown", "duration": "3732ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait for 10 ms", "status": "unknown", "duration": "10020ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "2421ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "4034ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "unknown", "duration": "2113ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "4544ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "unknown", "duration": "3838ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "2090ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "3925ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "7597ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "3486ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "unknown", "duration": "3689ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (34 steps)", "status": "unknown", "duration": "0ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "17439ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            98 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2191ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6038ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1173ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2100ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2602ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3177ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "2945ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "1886ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2434ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5327ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2276ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2361ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "2174ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1302ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2056ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2611ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3163ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2630ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "2914ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtStart Shopping\"]\" exists", "status": "passed", "duration": "1940ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtStart Shopping\"]", "status": "passed", "duration": "2183ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2529ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "1938ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2396ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5307ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "3076ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3270ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4129ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2712ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1935ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2566ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2176ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeStaticText[@name=\"Already a member?\"]\" is visible", "status": "passed", "duration": "16964ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3702ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1181ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2181ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2745ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname1]\"", "status": "passed", "duration": "3272ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2805ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3018ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "4451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2811ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5297ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2277ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3225ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2997ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2250ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1162ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2338ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5335ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2258ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2415ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4137ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2521ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1981ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2511ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5528ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2812ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1874ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2383ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "6830ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4434ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3294ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1131ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2629ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3147ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2637ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "2931ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "4847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2363ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2853ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1889ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2420ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2412ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2344ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3767ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5325ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2273ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3241ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "7465ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4404ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5023ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on Text: \"in\"", "status": "passed", "duration": "3210ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1118ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "sdqCYvk2Du", "screenshot_filename": "sdqCYvk2Du.png", "report_screenshot": "sdqCYvk2Du.png", "resolved_screenshot": "screenshots/sdqCYvk2Du.png", "clean_action_id": "sdqCYvk2Du", "prefixed_action_id": "al_sdqCYvk2Du", "action_id_screenshot": "screenshots/sdqCYvk2Du.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2457ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2566ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2377ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2370ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2369ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2500ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5289ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2289ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "24664ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            49 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2193ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5251ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1149ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2074ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2613ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname-op]\"", "status": "passed", "duration": "3201ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2624ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@6\"", "status": "passed", "duration": "2991ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1444ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4451ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2706ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2094ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2311ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "6599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2800ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "13515ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1989ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5736ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2453ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3256ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3972ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "iOS Function: text - Text: \"P_43386093\"", "status": "passed", "duration": "2522ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "3016ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2528ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5422ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2825ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2855ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1522ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2785ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "3045ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2214ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2805ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "2176ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1954ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2452ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2846ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "1921ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2394ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2347ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2473ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "3651ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2814ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "3656ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2729ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2175ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5328ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2947ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "24400ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod-Signin\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2197ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5337ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1136ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2067ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2606ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3168ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "2921ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2412ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5571ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2258ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1991ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4202ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1173ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2081ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3165ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "3301ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3316ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3075ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2744ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2393ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4248ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2270ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2014ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4964ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1165ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2102ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3011ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "3305ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10016ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "2032ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1692ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1671ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1645ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1647ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1644ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1618ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "5313ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2408ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5408ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2309ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1977ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4268ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1153ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2092ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Sign in with Google\"]\" is visible", "status": "passed", "duration": "5109ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2636ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2834ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2753ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2519ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5351ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2283ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1040ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23384ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            62 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2195ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5379ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1158ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2634ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2467ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2943ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5024ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "14913ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2321ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "4247ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "2624ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2284ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5019ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1499ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "2349ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "1443ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2287ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "11404ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5021ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "3171ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2463ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2435ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2231ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2948ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2319ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2949ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2256ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2960ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2468ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2206ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "3069ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "3039ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "3976ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "3933ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "3896ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "3078ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "3982ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "passed", "duration": "4003ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1606ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4763ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "3961ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2354ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "1942ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5306ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "3081ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3522ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "passed", "duration": "5271ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3447ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "passed", "duration": "1619ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2223ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on Text: \"Invite\"", "status": "passed", "duration": "2979ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "passed", "duration": "1701ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2763ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2812ms", "action_id": "sdqCYvk2Du", "screenshot_filename": "sdqCYvk2Du.png", "report_screenshot": "sdqCYvk2Du.png", "resolved_screenshot": "screenshots/sdqCYvk2Du.png", "clean_action_id": "sdqCYvk2Du", "prefixed_action_id": "al_sdqCYvk2Du", "action_id_screenshot": "screenshots/sdqCYvk2Du.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2207ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5439ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "3460ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "24195ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2188ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "2224ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "426ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "1210ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "1275ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1287ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2621ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2382ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "passed", "duration": "2216ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "passed", "duration": "2201ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "passed", "duration": "1571ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2363ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[uname-op]\"", "status": "passed", "duration": "1772ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "passed", "duration": "2367ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "passed", "duration": "1441ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2188ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2251ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1171ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2572ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname-op]\"", "status": "passed", "duration": "3171ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2678ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd-op]\"", "status": "passed", "duration": "2928ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "2659ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "passed", "duration": "2261ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "1347ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2390ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2919ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "3191ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "passed", "duration": "1505ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2428ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "passed", "duration": "1764ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "2825ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3227ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3646ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "3129ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"btnUpdate\"]", "status": "passed", "duration": "20113ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2269ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4126ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "iOS Function: text - Text: \"P_42691341\"", "status": "passed", "duration": "2515ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1823ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2352ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[atg-pdp]", "status": "passed", "duration": "2349ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Save my location\"]", "status": "passed", "duration": "10334ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "3750ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery Buy Steps (41 steps)", "status": "passed", "duration": "0ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2891ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3317ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2281ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1080ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23577ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            52 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2196ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5301ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1144ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2055ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2009ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2410ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4025ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3387ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "3173ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3067ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3156ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "6317ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3880ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2508ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1996ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3769ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "passed", "duration": "3149ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "3178ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3128ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3165ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with text=\"Sanctuary\" exists", "status": "passed", "duration": "13592ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2047ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2610ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "3183ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3927ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4028ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3225ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "3108ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3814ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "3173ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "11954ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5454ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1822ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2821ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[@name=\"Delivery options\"]/XCUIElementTypeButton[3]", "status": "passed", "duration": "1639ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2384ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "1927ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3568ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "5517ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "passed", "duration": "5245ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3473ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4568ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1723ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "4648ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "1780ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2262ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"Melbourne\" exists", "status": "passed", "duration": "15959ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2915ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5363ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2281ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "25208ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            61 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2209ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "2587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1171ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "1253ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1244ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2961ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5020ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "1171ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3290ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "276ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "738ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "221ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "653ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "226ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "645ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "179ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "141ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "770ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3232ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2309ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2711ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2872ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "3275ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1022ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart au\"", "status": "passed", "duration": "1662ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1157ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1449ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "2379ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2812ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2807ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4480ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2239ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "4875ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2513ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2970ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "20496ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2784ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4499ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2235ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5301ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2493ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "2676ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "2358ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2324ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2357ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2514ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]\" is visible", "status": "passed", "duration": "5057ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "2377ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "2364ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2118ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: accessibility_id=\"Add to bag\" (timeout: 15s) → Then tap at (0, 0)", "status": "passed", "duration": "3969ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3985ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: id=\"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" (timeout: 20s) → Then tap at (0, 0)", "status": "passed", "duration": "20233ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5020ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "24339ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            66 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2196ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2779ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "3112ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2783ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "18057ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "3481ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "3109ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2614ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "120ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2240ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4198ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "2567ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Execute Test Case: Click_Paginations (10 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3350ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2409ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2904ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2894ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2791ms", "action_id": "CcFsA41sKp", "screenshot_filename": "CcFsA41sKp.png", "report_screenshot": "CcFsA41sKp.png", "resolved_screenshot": "screenshots/CcFsA41sKp.png", "clean_action_id": "CcFsA41sKp", "prefixed_action_id": "al_CcFsA41sKp", "action_id_screenshot": "screenshots/CcFsA41sKp.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "4486ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3736ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2834ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4208ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1135ms", "action_id": "VJJ3EXXotU", "screenshot_filename": "VJJ3EXXotU.png", "report_screenshot": "VJJ3EXXotU.png", "resolved_screenshot": "screenshots/VJJ3EXXotU.png", "clean_action_id": "VJJ3EXXotU", "prefixed_action_id": "al_VJJ3EXXotU", "action_id_screenshot": "screenshots/VJJ3EXXotU.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2593ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3206ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3770ms", "action_id": "Wld5Urg70o", "screenshot_filename": "Wld5Urg70o.png", "report_screenshot": "Wld5Urg70o.png", "resolved_screenshot": "screenshots/Wld5Urg70o.png", "clean_action_id": "Wld5Urg70o", "prefixed_action_id": "al_Wld5Urg70o", "action_id_screenshot": "screenshots/Wld5Urg70o.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2662ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3992ms", "action_id": "wNGRrfUjpK", "screenshot_filename": "wNGRrfUjpK.png", "report_screenshot": "wNGRrfUjpK.png", "resolved_screenshot": "screenshots/wNGRrfUjpK.png", "clean_action_id": "wNGRrfUjpK", "prefixed_action_id": "al_wNGRrfUjpK", "action_id_screenshot": "screenshots/wNGRrfUjpK.png"}, {"name": "iOS Function: text - Text: \"enn[cooker-id]\"", "status": "passed", "duration": "2638ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1983ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2557ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2796ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"(\"]/following-sibling::*[1]", "status": "passed", "duration": "2871ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeStaticText[@name=\"Value\"])[1]\" exists", "status": "passed", "duration": "1855ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2862ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2874ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2056ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "2105ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2152ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2120ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2908ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2405ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "status": "passed", "duration": "2429ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1148ms", "action_id": "8XWyF2kgwW", "screenshot_filename": "8XWyF2kgwW.png", "report_screenshot": "8XWyF2kgwW.png", "resolved_screenshot": "screenshots/8XWyF2kgwW.png", "clean_action_id": "8XWyF2kgwW", "prefixed_action_id": "al_8XWyF2kgwW", "action_id_screenshot": "screenshots/8XWyF2kgwW.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1931ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Cancel\"", "status": "passed", "duration": "3691ms", "action_id": "HZT2s0AzX7", "screenshot_filename": "HZT2s0AzX7.png", "report_screenshot": "HZT2s0AzX7.png", "resolved_screenshot": "screenshots/HZT2s0AzX7.png", "clean_action_id": "HZT2s0AzX7", "prefixed_action_id": "al_HZT2s0AzX7", "action_id_screenshot": "screenshots/HZT2s0AzX7.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "3281ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on Text: \"Click\"", "status": "passed", "duration": "3290ms", "action_id": "zdh8hKYC1a", "screenshot_filename": "zdh8hKYC1a.png", "report_screenshot": "zdh8hKYC1a.png", "resolved_screenshot": "screenshots/zdh8hKYC1a.png", "clean_action_id": "zdh8hKYC1a", "prefixed_action_id": "al_zdh8hKYC1a", "action_id_screenshot": "screenshots/zdh8hKYC1a.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "status": "passed", "duration": "2478ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2575ms", "action_id": "9Pwdq32eUk", "screenshot_filename": "9Pwdq32eUk.png", "report_screenshot": "9Pwdq32eUk.png", "resolved_screenshot": "screenshots/9Pwdq32eUk.png", "clean_action_id": "9Pwdq32eUk", "prefixed_action_id": "al_9Pwdq32eUk", "action_id_screenshot": "screenshots/9Pwdq32eUk.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]\" is visible", "status": "passed", "duration": "11438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "status": "passed", "duration": "2420ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2241ms", "action_id": "sdqCYvk2Du", "screenshot_filename": "sdqCYvk2Du.png", "report_screenshot": "sdqCYvk2Du.png", "resolved_screenshot": "screenshots/sdqCYvk2Du.png", "clean_action_id": "sdqCYvk2Du", "prefixed_action_id": "al_sdqCYvk2Du", "action_id_screenshot": "screenshots/sdqCYvk2Du.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"About KHub Stores\"]", "status": "passed", "duration": "2437ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2217ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Swipe from (50%, 30%) to (50%, 70%)", "status": "passed", "duration": "2926ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2460ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2305ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2527ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "2253ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1218ms", "action_id": "M3dXqigqRv", "screenshot_filename": "M3dXqigqRv.png", "report_screenshot": "M3dXqigqRv.png", "resolved_screenshot": "screenshots/M3dXqigqRv.png", "clean_action_id": "M3dXqigqRv", "prefixed_action_id": "al_M3dXqigqRv", "action_id_screenshot": "screenshots/M3dXqigqRv.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1052ms", "action_id": "AeQaElnzUN", "screenshot_filename": "AeQaElnzUN.png", "report_screenshot": "AeQaElnzUN.png", "resolved_screenshot": "screenshots/AeQaElnzUN.png", "clean_action_id": "AeQaElnzUN", "prefixed_action_id": "al_AeQaElnzUN", "action_id_screenshot": "screenshots/AeQaElnzUN.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23481ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}], "passed": 9, "failed": 1, "skipped": 0, "status": "failed"}