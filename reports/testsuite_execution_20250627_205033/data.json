{"name": "Unique ID Demo Suite", "test_execution_id": "ex_20250627_205033", "test_suite_id": "ts_demo123456", "timestamp": "2025-06-27T20:50:33.681589", "status": "passed", "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "testCases": [{"name": "<PERSON>ple Test Case with <PERSON><PERSON>", "test_case_id": "tc_6675d72d00fe", "status": "passed", "filename": "sample_test_case.json", "steps": [{"action_id": "ac_001", "action_type": "tap", "status": "passed", "retry_count": 3, "test_execution_id": "ex_ba77d8b96e65", "end_time": "2025-06-27T20:44:41.361914", "execution_result": "Successfully tapped login button"}, {"action_id": "ac_001", "action_type": "tap", "status": "failed", "retry_count": 2, "test_execution_id": "ex_ba77d8b96e65", "end_time": "2025-06-27T20:44:37.361914", "execution_result": "Failed to find login button on retry"}, {"action_id": "ac_001", "action_type": "tap", "status": "failed", "retry_count": 1, "test_execution_id": "ex_ba77d8b96e65", "end_time": "2025-06-27T20:44:32.361914", "execution_result": "Failed to find login button"}]}]}